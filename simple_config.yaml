# 简化的FlexProxy配置文件用于测试
global:
  enable: true
  proxy_file: "./proxies.txt"
  rule_priority: 100
  default_process_stage: "post_body"
  dns_lookup_mode: "system"
  reverse_dns_lookup: "no"
  custom_dns_servers:
    - server: "*******"  # 注意：验证器要求纯IP地址
      protocol: "udp"
      timeout: 5000
      priority: 1
      tags: ["google"]
  http_proxy_dns: "*******:53"
  ip_rotation_mode: "random"
  min_proxy_pool_size: 5
  max_proxy_fetch_attempts: 3
  dns_cache_ttl: 300000
  ip_version_priority: "ipv4"
  default_dns_timeout: 5000
  retry_proxy_reuse_policy: "allow"
  retry_proxy_cooldown_time: 30000

# 简单的动作序列
actions:
  simple_log:
    sequence:
      - type: "log"
        message: "测试日志"
        level: "info"

# 简单的事件配置
events:
  - name: "test_event"
    enable: true
    trigger_type: "status"
    process_stage: "post_body"
    priority: 10
    conditions:
      - name: "test_condition"
        enable: true
        status_codes:
          codes: [200]
          relation: "or"
    matches:
      - name: "test_match"
        enable: true
        conditions: ["test_condition"]
        actions:
          - type: "log"
            message: "测试匹配成功"
            level: "info"
