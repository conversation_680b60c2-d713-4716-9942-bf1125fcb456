package main

import (
	"fmt"
	"io/ioutil"
	"os"

	"gopkg.in/yaml.v3"
)

func main() {
	configFile := "complete_config.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	// 读取 YAML 文件
	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		fmt.Printf("Error reading file %s: %v\n", configFile, err)
		os.Exit(1)
	}

	// 解析 YAML
	var config interface{}
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("YAML syntax error in %s: %v\n", configFile, err)
		os.Exit(1)
	}

	fmt.Printf("YAML file %s is valid!\n", configFile)
}