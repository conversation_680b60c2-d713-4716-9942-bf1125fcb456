package action

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv" // 用于BuildActionFromConfig中的字符串转换
	"strings"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"         // 用于日志记录
	"github.com/mubeng/mubeng/internal/interfaces"   // 接口定义包
	// "github.com/mubeng/mubeng/internal/proxymanager" // 不再需要直接导入
	// "github.com/mubeng/mubeng/pkg/config" // 旧的配置常量可能通过字符串字面量引用（如果未更新）
)

// 模块级别的日志器
var actionLogger = logger.GetActionLogger()

// 新的依赖注入架构相关结构

// ActionDefinition 新的动作定义（用于依赖注入架构）
type ActionDefinition struct {
	Name         string                 `json:"name"`
	Type         string                 `json:"type"`
	Enabled      bool                   `json:"enabled"`
	Parameters   map[string]interface{} `json:"parameters"`
	Description  string                 `json:"description"`
	Executor     Executor               `json:"-"`
	CreatedAt    time.Time              `json:"created_at"`
	LastExecuted time.Time              `json:"last_executed"`
	ExecuteCount int                    `json:"execute_count"`
	ErrorCount   int                    `json:"error_count"`
}

// Executor 动作执行器接口
type Executor interface {
	// Execute 执行动作
	Execute(ctx context.Context, parameters map[string]interface{}) error
	
	// Validate 验证参数
	Validate(parameters map[string]interface{}) error
	
	// GetType 获取执行器类型
	GetType() string
	
	// GetDescription 获取执行器描述
	GetDescription() string
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	ActionName string
	Context    context.Context
	Callback   func(error)
	Timestamp  time.Time
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ActionName string
	Success    bool
	Error      error
	Duration   time.Duration
	Timestamp  time.Time
}

// 内置执行器实现

// LogExecutor 日志执行器
type LogExecutor struct {
	Logger interfaces.LogService
}

func (e *LogExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	message, ok := parameters["message"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少message参数")
	}
	
	level, _ := parameters["level"].(string)
	if level == "" {
		level = "info"
	}
	
	switch level {
	case "debug":
		e.Logger.Debug(message)
	case "warn":
		e.Logger.Warn(message)
	case "error":
		e.Logger.Error(message)
	default:
		e.Logger.Info(message)
	}
	
	return nil
}

func (e *LogExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["message"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的message参数")
	}
	return nil
}

func (e *LogExecutor) GetType() string {
	return "log"
}

func (e *LogExecutor) GetDescription() string {
	return "记录日志消息"
}

// BanIPExecutor IP封禁执行器
type BanIPExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanIPExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	ip, ok := parameters["ip"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少ip参数")
	}
	
	durationStr, _ := parameters["duration"].(string)
	if durationStr == "" {
		durationStr = "1h"
	}
	
	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeInvalidParameter, "无效的duration参数")
	}
	
	durationSeconds := int(duration.Seconds())
	e.ProxyService.BanIP(ip, durationSeconds)
	e.Logger.Info("IP已被封禁: %s, 持续时间: %d秒", ip, durationSeconds)
	return nil
}

func (e *BanIPExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["ip"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的ip参数")
	}
	return nil
}

func (e *BanIPExecutor) GetType() string {
	return "ban_ip"
}

func (e *BanIPExecutor) GetDescription() string {
	return "封禁指定IP地址"
}

// BanDomainExecutor 封禁域名执行器
type BanDomainExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanDomainExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	domain, ok := parameters["domain"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少domain参数")
	}
	
	durationStr, _ := parameters["duration"].(string)
	if durationStr == "" {
		durationStr = "1h"
	}
	
	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeInvalidParameter, "无效的duration参数")
	}
	
	durationSeconds := int(duration.Seconds())
	e.ProxyService.BanDomain(domain, durationSeconds)
	e.Logger.Info("域名已被封禁: %s, 持续时间: %d秒", domain, durationSeconds)
	return nil
}

func (e *BanDomainExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["domain"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的domain参数")
	}
	return nil
}

func (e *BanDomainExecutor) GetType() string {
	return "ban_domain"
}

func (e *BanDomainExecutor) GetDescription() string {
	return "封禁指定域名"
}

// BlockRequestExecutor 阻止请求执行器
type BlockRequestExecutor struct {
	Logger interfaces.LogService
}

func (e *BlockRequestExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	reason, _ := parameters["reason"].(string)
	if reason == "" {
		reason = "请求被阻止"
	}
	
	e.Logger.Info("请求已被阻止: %s", reason)
	return errors.NewErrorWithDetails(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "请求被阻止", fmt.Sprintf("原因: %s", reason))
}

func (e *BlockRequestExecutor) Validate(parameters map[string]interface{}) error {
	return nil // 无必需参数
}

func (e *BlockRequestExecutor) GetType() string {
	return "block_request"
}

func (e *BlockRequestExecutor) GetDescription() string {
	return "阻止当前请求"
}

// ModifyRequestExecutor 修改请求执行器
type ModifyRequestExecutor struct {
	Logger interfaces.LogService
}

func (e *ModifyRequestExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 这里应该实现请求修改逻辑
	// 由于需要访问HTTP请求对象，这个执行器可能需要特殊处理
	e.Logger.Info("请求修改动作已执行")
	return nil
}

func (e *ModifyRequestExecutor) Validate(parameters map[string]interface{}) error {
	return nil
}

func (e *ModifyRequestExecutor) GetType() string {
	return "modify_request"
}

func (e *ModifyRequestExecutor) GetDescription() string {
	return "修改HTTP请求"
}

// ModifyResponseExecutor 修改响应执行器
type ModifyResponseExecutor struct {
	Logger interfaces.LogService
}

func (e *ModifyResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 这里应该实现响应修改逻辑
	e.Logger.Info("响应修改动作已执行")
	return nil
}

func (e *ModifyResponseExecutor) Validate(parameters map[string]interface{}) error {
	return nil
}

func (e *ModifyResponseExecutor) GetType() string {
	return "modify_response"
}

func (e *ModifyResponseExecutor) GetDescription() string {
	return "修改HTTP响应"
}

// CacheResponseExecutor 缓存响应执行器
type CacheResponseExecutor struct {
	Logger       interfaces.LogService
	CacheService interfaces.CacheService
}

func (e *CacheResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	key, ok := parameters["key"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少key参数")
	}
	
	ttlStr, _ := parameters["ttl"].(string)
	if ttlStr == "" {
		ttlStr = "1h"
	}
	
	ttl, err := time.ParseDuration(ttlStr)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeInvalidParameter, "无效的ttl参数")
	}
	
	// 这里应该实现响应缓存逻辑
	e.Logger.Info("响应缓存动作已执行: key=%s, ttl=%v", key, ttl)
	return nil
}

func (e *CacheResponseExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["key"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的key参数")
	}
	return nil
}

func (e *CacheResponseExecutor) GetType() string {
	return "cache_response"
}

func (e *CacheResponseExecutor) GetDescription() string {
	return "缓存HTTP响应"
}

// ScriptExecutor 脚本执行器
type ScriptExecutor struct {
	Logger interfaces.LogService
}

func (e *ScriptExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	script, ok := parameters["script"].(string)
	if !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少script参数")
	}
	
	// 这里应该实现脚本执行逻辑（如JavaScript执行）
	e.Logger.Info("脚本执行动作已执行: %s", script)
	return nil
}

func (e *ScriptExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["script"]; !ok {
		return errors.NewError(errors.ErrTypeAction, errors.ErrCodeMissingParameter, "缺少必需的script参数")
	}
	return nil
}

func (e *ScriptExecutor) GetType() string {
	return "script"
}

func (e *ScriptExecutor) GetDescription() string {
	return "执行自定义脚本"
}

// 全局缓存存储
var (
	responseCache     = make(map[string]*CachedResponse)
	responseCacheLock sync.RWMutex
)

// CachedResponse 存储缓存的响应
type CachedResponse struct {
	Response        *http.Response
	Body            []byte
	ExpireAt        time.Time
	MaxUseCount     int       // 最大使用次数，0表示无限制
	CurrentUseCount int       // 当前已使用次数
	CreatedAt       time.Time // 创建时间
	LastUsedAt      time.Time // 最后使用时间
	CacheKey        string    // 缓存键
}

// Action 动作接口
type Action interface {
	Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error)
}

// RetrySameAction 使用相同IP重试
type RetrySameAction struct {
	RetryCount int
}

// Execute 实现RetrySameAction的执行逻辑
func (a *RetrySameAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	// 使用模块级别的logger实例，避免重复调用GetLogger
	logger.GetActionLogger().GetRawLogger().Debugf("RetrySameAction: Entered Execute. Receiver a: %p, ctx: %p, req: %p", a, ctx, req)
	currentProxyVal := ctx.Value("current_proxy")
	logger.GetActionLogger().GetRawLogger().Debugf("RetrySameAction: Value for 'current_proxy' from context: %v (Type: %T)", currentProxyVal, currentProxyVal)

	currentProxy, ok := currentProxyVal.(string)
	logger.GetActionLogger().GetRawLogger().Debugf("RetrySameAction: After type assertion to string - currentProxy: '%s', ok: %v", currentProxy, ok)

	if !ok || currentProxy == "" {
		logger.GetActionLogger().GetRawLogger().Errorf("RetrySameAction执行失败: 无法获取当前代理IP. ok: %v, currentProxy: '%s'", ok, currentProxy)
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "无法获取当前代理IP")
	}
	logger.GetActionLogger().GetRawLogger().Infof("RetrySameAction: 标记使用相同代理 %s 重试 %d 次", currentProxy, a.RetryCount)
	// 添加诊断日志
	if req != nil {
		logger.GetActionLogger().GetRawLogger().Debugf("RetrySameAction: req before WithContext (Method: %s, URL: %s, Host: %s)", req.Method, req.URL, req.Host)
	} else {
		actionLogger.Debug("RetrySameAction: req before WithContext is nil")
	}
	logger.GetActionLogger().GetRawLogger().Debugf("RetrySameAction: ctx before WithContext: %T, %#v", ctx, ctx)

	retryCtx := context.WithValue(ctx, "retry_same_count", a.RetryCount)
	retryCtx = context.WithValue(retryCtx, "retry_same_proxy", currentProxy)
	retryCtx = context.WithValue(retryCtx, "retry_action_type", "retry_same")
	retryCtx = context.WithValue(retryCtx, "action_requires_retry", true)
	logger.GetActionLogger().GetRawLogger().Debugf("RetrySameAction: retryCtx to be used: %T, %#v", retryCtx, retryCtx)

	// 关键行
	*req = *req.WithContext(retryCtx)
	actionLogger.Info("RetrySameAction: 动作执行成功，已标记需要重试")
	return nil, nil
}

// RetryAction 使用新IP重试
type RetryAction struct {
	RetryCount int
}

// Execute 实现RetryAction的执行逻辑
func (a *RetryAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	// 使用模块级别的logger实例，避免重复调用GetLogger
	logger.GetActionLogger().GetRawLogger().Infof("RetryAction: 标记使用新IP重试 %d 次", a.RetryCount)
	retryCtx := context.WithValue(ctx, "retry_count", a.RetryCount)
	retryCtx = context.WithValue(retryCtx, "retry_action_type", "retry")
	retryCtx = context.WithValue(retryCtx, "action_requires_retry", true)
	*req = *req.WithContext(retryCtx)
	actionLogger.Info("RetryAction: 动作执行成功，已标记需要使用新IP重试")
	return nil, nil
}

// BanIPAction 全局封禁IP
type BanIPAction struct {
	Duration string // 可以是毫秒数或"reboot"
}

// Execute 实现BanIPAction的执行逻辑
func (a *BanIPAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	if resetter, ok := ctx.Value("proxy_resetter").(interfaces.ProxyStateResetter); ok && resetter != nil {
		resetter.ResetProxyState()
	}
	if pm == nil {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "ProxyManager为nil")
	}
	proxyURL, ok := ctx.Value("current_proxy").(string)
	if !ok || proxyURL == "" {
		proxyURL = req.Header.Get("X-Forwarded-For")
		if proxyURL == "" {
			return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "无法获取当前代理IP")
		}
	}
	// 如果proxyURL是完整URL，提取主机名
	ipToBan := proxyURL
	if u, err := url.Parse(proxyURL); err == nil && u.Host != "" {
		ipToBan = u.Hostname()
	}

	err := pm.BanIP(ipToBan, a.Duration, "global", "") // BanIP在ProxyManagerInterface中
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "封禁IP失败", fmt.Sprintf("IP: %s", ipToBan))
	}
	// 使用模块级别的logger实例
	logger.GetActionLogger().GetRawLogger().Infof("成功封禁IP: %s，时长: %s", ipToBan, a.Duration)
	_ = pm.RemoveProxy(proxyURL) // RemoveProxy在ProxyManagerInterface中
	return nil, nil
}

// BanIPDomainAction 针对域名封禁IP
type BanIPDomainAction struct {
	Duration string
	Scope    string // "url"、"domain"、"global"
}

// Execute 实现BanIPDomainAction的执行逻辑
func (a *BanIPDomainAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	if pm == nil {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "ProxyManager为nil")
	}
	if resetter, ok := ctx.Value("proxy_resetter").(interfaces.ProxyStateResetter); ok && resetter != nil {
		resetter.ResetProxyState()
	}
	proxyURL, ok := ctx.Value("current_proxy").(string)
	if !ok || proxyURL == "" {
		proxyURL = req.Header.Get("X-Forwarded-For")
		if proxyURL == "" {
			return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "无法获取当前代理IP")
		}
	}
	ipToBan := proxyURL
	if u, err := url.Parse(proxyURL); err == nil && u.Host != "" {
		ipToBan = u.Hostname()
	}

	targetResource := ""
	actualScope := strings.ToLower(a.Scope)
	switch actualScope {
	case "url":
		targetResource = req.URL.String()
	case "domain":
		targetResource = req.URL.Hostname()
		if targetResource == "" {
			return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "无法获取当前请求域名")
		}
	case "global":
		// targetResource保持为""
	default:
		logger.GetActionLogger().GetRawLogger().Warnf("未知的 BanIPDomainAction scope '%s', 默认为 'domain'", a.Scope)
		actualScope = "domain"
		targetResource = req.URL.Hostname()
		if targetResource == "" {
			return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "无法获取当前请求域名")
		}
	}
	err := pm.BanIP(ipToBan, a.Duration, actualScope, targetResource) // BanIP在ProxyManagerInterface中
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "级别封禁IP失败", fmt.Sprintf("级别: %s, IP: %s", actualScope, ipToBan))
	}
	logger.GetActionLogger().GetRawLogger().Infof("成功执行 %s 级别封禁，IP: %s，资源: '%s'，时长: %s", actualScope, ipToBan, targetResource, a.Duration)
	// 考虑从通用池中移除代理（如果全局封禁或针对特定域名）
	// pm.RemoveProxyFromDomain(proxyURL, targetResource) 或 pm.RemoveProxy(proxyURL)
	return nil, nil
}

// SaveToPoolAction 智能代理质量管理系统
type SaveToPoolAction struct {
	QualityTier    string
	DomainSpecific bool
	MinScore       float64
}

// Execute 实现SaveToPoolAction的执行逻辑
func (a *SaveToPoolAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	proxyURL, _ := ctx.Value("current_proxy").(string)
	if proxyURL == "" {
		proxyURL = req.Header.Get("X-Forwarded-For")
		if proxyURL == "" {
			return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "SaveToPoolAction: 无法获取当前代理IP")
		}
	}
	domain := req.URL.Hostname()
	responseTime, _ := ctx.Value("response_time").(time.Duration)
	if responseTime == 0 {
		responseTime = 1000 * time.Millisecond
	} // 如果未找到则使用默认值
	success := resp != nil && resp.StatusCode >= 200 && resp.StatusCode < 400

	pm.UpdateProxyQuality(proxyURL, success, responseTime, domain) // UpdateProxyQuality在ProxyManagerInterface中
	// qualityInfo, exists := pm.GetProxyQualityInfo(proxyURL) // GetProxyQualityInfo尚未在接口中
	// 目前，我们将在没有qualityInfo的情况下继续，或假设它以不同方式处理。
	// 如果下面使用qualityInfo，这可能会导致编译错误，我们将解决这个问题。
	// 现在假设动作可以继续，或者这一步不严格需要qualityInfo。
	var qualityInfoScore float64 = 0       // 占位符
	var qualityInfoTier string = "unknown" // 占位符
	var exists bool = false                // 占位符

	// 尝试通过接口方法获取代理质量信息
	// 注意：如果接口中没有GetProxyQualityInfo方法，这里需要添加或使用其他方式
	// 暂时设置默认值
	qualityInfoScore = 50.0 // 默认评分
	qualityInfoTier = "standard" // 默认等级
	exists = true
	logger.GetActionLogger().GetRawLogger().Info("SaveToPoolAction: 使用默认质量信息，因为接口中暂未提供GetProxyQualityInfo方法")
	if !exists {
		logger.GetActionLogger().GetRawLogger().Warnf("SaveToPoolAction: 更新后无法获取代理 %s 的质量信息", proxyURL)
		return nil, nil
	}
	if exists && a.MinScore > 0 && qualityInfoScore < a.MinScore {
		logger.GetActionLogger().GetRawLogger().Infof("SaveToPoolAction: 代理 %s 质量评分 %.2f 低于要求 %.2f", proxyURL, qualityInfoScore, a.MinScore)
		return nil, nil
	}
	if !exists && a.MinScore > 0 { // 如果我们无法获取质量信息但需要最小分数
		logger.GetActionLogger().GetRawLogger().Warnf("SaveToPoolAction: 无法获取代理 %s 的质量信息，但定义了最低分数要求 %.2f", proxyURL, a.MinScore)
		// 决定这应该是错误还是警告。现在继续。
	}
	// 如果不存在，添加到主池的逻辑可以是ProxyManager的AddProxy或类似方法的一部分
	if a.DomainSpecific && success && domain != "" {
		pm.AddToDomainPool(proxyURL, domain) // AddToDomainPool在ProxyManagerInterface中
	}
	if exists {
		logger.GetActionLogger().GetRawLogger().Infof("SaveToPoolAction: 代理 %s 处理完毕. 评分: %.2f, 等级: %s", proxyURL, qualityInfoScore, qualityInfoTier)
	} else {
		logger.GetActionLogger().GetRawLogger().Infof("SaveToPoolAction: 代理 %s 处理完毕 (质量信息获取失败或非具体类型)", proxyURL)
	}
	return nil, nil
}

// QualityProxyAction 智能代理选择动作
type QualityProxyAction struct {
	PreferredTier string
	FallbackTier  string
	DomainFirst   bool
}

// Execute 实现QualityProxyAction的执行逻辑
func (a *QualityProxyAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	domain := req.URL.Hostname()
	var proxy string
	var err error

	if a.DomainFirst && domain != "" {
		proxy, _ = pm.GetProxyForDomain("random", domain) // GetProxyForDomain在ProxyManagerInterface中
		if proxy != "" {
		logger.GetActionLogger().GetRawLogger().Infof("QualityProxyAction: 从域名 %s 专用池选择代理 %s", domain, proxy)
	}
	}
	if proxy == "" {
		proxy, err = pm.GetQualityProxy(a.PreferredTier, domain) // GetQualityProxy在ProxyManagerInterface中
		if err != nil && a.FallbackTier != "" {
			logger.GetActionLogger().GetRawLogger().Infof("QualityProxyAction: 首选等级 %s 失败，尝试备选等级 %s (域名: %s)", a.PreferredTier, a.FallbackTier, domain)
			proxy, err = pm.GetQualityProxy(a.FallbackTier, domain) // GetQualityProxy在ProxyManagerInterface中
		}
	}
	if err != nil || proxy == "" {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "QualityProxyAction: 无法获取质量代理", fmt.Sprintf("偏好: %s, 域名: %s", a.PreferredTier, domain))
	}
	newCtx := context.WithValue(ctx, "selected_quality_proxy", proxy)
	newCtx = context.WithValue(newCtx, "current_proxy", proxy) // 同时为同一序列中的后续动作更新current_proxy
	// ... (其他上下文值如quality_tier_used, domain_first_used)
	*req = *req.WithContext(newCtx)
	// 获取质量等级用于日志记录（如果可用）
	var qualityTierForLog string = "N/A"
	// 暂时使用默认质量等级，因为接口中暂未提供GetProxyQualityInfo方法
	qualityTierForLog = "standard" // 默认等级
	logger.GetActionLogger().GetRawLogger().Info("QualityProxyAction: 使用默认质量等级，因为接口中暂未提供GetProxyQualityInfo方法")
	// 以下行有问题，基于注释掉的if块。
	// qInfo在这里不会被定义。
	// qualityTierForLog = qInfo.QualityTier
	// }
	logger.GetActionLogger().GetRawLogger().Infof("QualityProxyAction: 为域名 %s 选择了代理 %s (等级: %s)", domain, proxy, qualityTierForLog)
	return nil, nil
}

// CacheAction 增强版缓存响应内容
type CacheAction struct {
	Duration     int // 毫秒
	MaxUseCount  int
	CacheScope   string
	CustomKey    string
	IgnoreParams bool
}

// Execute 实现CacheAction的增强执行逻辑
func (a *CacheAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	if resp == nil {
		return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "无法缓存空响应")
	}
	cacheKey := a.generateAdvancedCacheKey(req)

	responseCacheLock.RLock()
	cached, exists := responseCache[cacheKey]
	responseCacheLock.RUnlock()

	if exists {
		now := time.Now()
		// 如果a.Duration为0，表示缓存应该是永久的（或由ExpireAt处理的很长过期时间）。
		// 如果cached.ExpireAt为零，也意味着它可能是来自旧逻辑的永久缓存或不应过期。
		// 缓存要有效的时间条件：
		// 1. 如果a.Duration（来自动作配置）为0，则认为时间有效（永久）。
		// 2. 如果a.Duration > 0，则'now'必须在'cached.ExpireAt'之前。
		timeValid := false
		if a.Duration == 0 { // 动作配置为永久缓存
			timeValid = true
		} else { // 动作配置了特定持续时间
			timeValid = now.Before(cached.ExpireAt)
		}
		countValid := cached.MaxUseCount == 0 || cached.CurrentUseCount < cached.MaxUseCount
		if timeValid && countValid {
			responseCacheLock.Lock()
			cached.CurrentUseCount++
			cached.LastUsedAt = now
			responseCacheLock.Unlock()
			logger.GetActionLogger().GetRawLogger().Infof("CacheAction: 命中缓存 %s", cacheKey)
			return cloneResponse(cached.Response, cached.Body), nil
		}
		responseCacheLock.Lock()
		delete(responseCache, cacheKey)
		responseCacheLock.Unlock()
		logger.GetActionLogger().GetRawLogger().Infof("CacheAction: 缓存 %s 无效/过期，已删除", cacheKey)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "读取响应体失败")
	}
	resp.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // 恢复响应体

	var expireAt time.Time
	if a.Duration > 0 {
		expireAt = time.Now().Add(time.Duration(a.Duration) * time.Millisecond)
	} else { // Duration为0表示永久（或很长的过期时间）
		expireAt = time.Now().Add(100 * 365 * 24 * time.Hour) // 实际上是永久的
	}
	now := time.Now()
	newCachedItem := &CachedResponse{
		Response:        cloneResponse(resp, bodyBytes), // 存储克隆
		Body:            bodyBytes,
		ExpireAt:        expireAt,
		MaxUseCount:     a.MaxUseCount,
		CurrentUseCount: 0, // Initial use count is 0, will be incremented on hit
		CreatedAt:       now,
		LastUsedAt:      now,
		CacheKey:        cacheKey,
		// Duration字段被错误地添加到CachedResponse结构体字面量中。
		// CachedResponse没有Duration字段。使用ExpireAt。
	}
	responseCacheLock.Lock()
	responseCache[cacheKey] = newCachedItem
	responseCacheLock.Unlock()
	logger.GetActionLogger().GetRawLogger().Infof("CacheAction: 新建缓存 %s", cacheKey)
	return nil, nil // 缓存动作本身不会在这里修改响应流，只是为下次缓存
}

func (a *CacheAction) generateAdvancedCacheKey(req *http.Request) string {
	keyParts := []string{a.CacheScope}
	switch a.CacheScope {
	case "url":
		u := *req.URL
		if a.IgnoreParams {
			u.RawQuery = ""
		}
		keyParts = append(keyParts, u.String())
	case "domain":
		keyParts = append(keyParts, req.URL.Hostname())
	case "custom":
		if a.CustomKey != "" { // TODO: 实现CustomKey的模板评估
			return strings.ReplaceAll(a.CustomKey, "{url}", req.URL.String()) // 简单示例
		}
		keyParts = append(keyParts, req.URL.String()) // 回退
	default: // 全局或未知
		keyParts = append(keyParts, req.Method+":"+req.URL.Path) // 全局示例
	}
	return strings.Join(keyParts, "::")
}

// RequestURLAction 增强版请求指定URL动作
type RequestURLAction struct {
	URL                 string            `mapstructure:"url"`
	Method              string            `mapstructure:"method"`
	Body                string            `mapstructure:"body"`
	BodyType            string            `mapstructure:"body_type"`    // "json", "form", "raw", "copy"
	Headers             string            `mapstructure:"headers"`      // "Key1:Value1,Key2:Value2"
	TimeoutMS           int               `mapstructure:"timeout_ms"`   // 请求超时时间（毫秒）
	ProxyOption         string            `mapstructure:"proxy_option"` // "current", "new", "none", "quality:<tier>"
	FollowRedirect      bool              `mapstructure:"follow_redirect"`
	MaxRedirects        int               `mapstructure:"max_redirects"`
	RetryCount          int               `mapstructure:"retry_count"`
	RetryDelayMS        int               `mapstructure:"retry_delay_ms"`
	ValidateSSL         bool              `mapstructure:"validate_ssl"`
	UserAgent           string            `mapstructure:"user_agent"`
	CopyHeaders         bool              `mapstructure:"copy_headers"` // 从原始请求复制头部
	SaveResponse        bool              `mapstructure:"save_response"`
	SaveResponsePath    string            `mapstructure:"save_response_path"` // 保存响应体的路径
	ValidateStatusCodes []int             `mapstructure:"validate_status_codes"`
	ExtractHeaders      map[string]string `mapstructure:"extract_headers"` // map[要提取的头部]上下文键名
	// LogLevel string // 日志记录由全局记录器或上下文处理，通常不是每个动作
}

// 执行RequestURLAction
func (a *RequestURLAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	logger.GetActionLogger().GetRawLogger().Infof("RequestURLAction: %s to %s", a.Method, a.URL)

	// 1. Build Target URL (handle templates if any)
	targetURL := a.URL // Simplified, add template processing if needed

	// 2. Prepare Request Body
	var reqBody io.Reader
	if a.Body != "" {
		// TODO: Handle BodyType: json, form, raw, copy
		reqBody = strings.NewReader(a.Body)
	} else if a.BodyType == "copy" && req.Body != nil {
		bodyBytes, err := io.ReadAll(req.Body)
		if err != nil {
			return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "RequestURLAction: 读取原始请求体失败")
		}
		req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // 恢复原始请求体
		reqBody = bytes.NewBuffer(bodyBytes)
	}

	// 3. Create HTTP Request
	httpReq, err := http.NewRequestWithContext(ctx, strings.ToUpper(a.Method), targetURL, reqBody)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "RequestURLAction: 创建新请求失败")
	}

	// 4. Set Headers
	// TODO: Parse a.Headers string and set them. Handle CopyHeaders logic.
	// For now, copy original request's User-Agent if not set
	if a.Headers != "" {
		headerPairs := strings.Split(a.Headers, ",")
		for _, pair := range headerPairs {
			kv := strings.SplitN(pair, ":", 2)
			if len(kv) == 2 {
				httpReq.Header.Set(strings.TrimSpace(kv[0]), strings.TrimSpace(kv[1]))
			}
		}
	} else if req.Header.Get("User-Agent") != "" { // 基本头部复制
		httpReq.Header.Set("User-Agent", req.Header.Get("User-Agent"))
	}

	// 5. Create HTTP Client (handle proxy, timeout, SSL validation)
	client := http.Client{
		Timeout: time.Duration(a.TimeoutMS) * time.Millisecond,
		// TODO: 添加传输层处理代理 (a.ProxyOption) 和 SSL (a.ValidateSSL)
		// TODO: 处理重定向 (a.FollowRedirect, a.MaxRedirects)
	}
	if !a.FollowRedirect {
		client.CheckRedirect = func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}
	}

	// 6. Execute Request (handle retries)
	// TODO: 实现重试逻辑 (a.RetryCount, a.RetryDelayMS)
	actionResp, err := client.Do(httpReq)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "RequestURLAction: 请求执行失败")
	}

	// 7. Validate Status (if needed)
	// TODO: 实现 a.ValidateStatus 逻辑

	// 8. Extract Headers (if needed)
	// TODO: 实现 a.ExtractHeaders 逻辑，可能存储在上下文中

	// This action typically replaces the current response flow.
	return actionResp, nil
}

// NullResponseAction 返回空响应或自定义响应
type NullResponseAction struct {
	StatusCode  int
	ContentType string
	Body        string
	Headers     string // "Key1:Value1,Key2:Value2"
}

// 执行NullResponseAction
func (a *NullResponseAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	logger.GetActionLogger().GetRawLogger().Infof("NullResponseAction: Returning custom response with status %d", a.StatusCode)
	newResp := &http.Response{
		StatusCode: a.StatusCode,
		Proto:      "HTTP/1.1", ProtoMajor: 1, ProtoMinor: 1,
		Header:  make(http.Header),
		Request: req,
		Body:    io.NopCloser(strings.NewReader(a.Body)),
	}
	if a.ContentType != "" {
		newResp.Header.Set("Content-Type", a.ContentType)
	}
	if a.Headers != "" {
		headerParts := strings.Split(a.Headers, ",")
		for _, hp := range headerParts {
			kv := strings.SplitN(hp, ":", 2)
			if len(kv) == 2 {
				newResp.Header.Set(strings.TrimSpace(kv[0]), strings.TrimSpace(kv[1]))
			}
		}
	}
	newResp.ContentLength = int64(len(a.Body))
	return newResp, nil
}

// BypassProxyAction 绕过代理直接连接
type BypassProxyAction struct {
	Timeout time.Duration
}

// 执行BypassProxyAction
func (a *BypassProxyAction) Execute(ctx context.Context, req *http.Request, resp *http.Response, pm interfaces.ProxyManagerInterface) (*http.Response, error) {
	logger.GetActionLogger().GetRawLogger().Infof("BypassProxyAction: Bypassing proxy for %s", req.URL.String())
	// 确保原始请求体在必要时可以多次读取
	var bodyBytes []byte
	var err error
	if req.Body != nil {
		bodyBytes, err = io.ReadAll(req.Body)
		if err != nil {
			return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "BypassProxyAction: 读取请求体失败")
		}
		// Restore the original request body so it's not consumed
		req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	directReq, err := http.NewRequestWithContext(ctx, req.Method, req.URL.String(), bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "BypassProxyAction: 创建直接请求失败")
	}
	directReq.Header = req.Header.Clone()

	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true}, // 简化版本
	}
	client := http.Client{Transport: transport, Timeout: a.Timeout}
	actionResp, err := client.Do(directReq)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "BypassProxyAction: 直接请求失败")
	}
	return actionResp, nil
}

// ActionManager 管理和执行动作
type ActionManager struct {
	// No longer needs to store config for pre-parsing
}

// NewActionManager 创建一个新的ActionManager
func NewActionManager() *ActionManager {
	return &ActionManager{}
}

// BuildActionFromConfig creates an Action instance from common.ActionConfig.
func (am *ActionManager) BuildActionFromConfig(actionCfg common.ActionConfig) (Action, error) {
	params := actionCfg.Params
	actionType := strings.ToLower(actionCfg.Type)

	switch actionType {
	case "retry_same":
		var count int = 1
		if val, ok := params["attempts"]; ok {
			switch v := val.(type) {
			case float64:
				count = int(v)
			case int:
				count = v
			case string:
				if c, err := strconv.Atoi(v); err == nil {
					count = c
				} else {
				logger.GetActionLogger().GetRawLogger().Warnf("Invalid 'attempts' for retry_same: %s", v)
			}
		default:
			logger.GetActionLogger().GetRawLogger().Warnf("Unsupported 'attempts' type for retry_same: %T", v)
			}
		}
		return &RetrySameAction{RetryCount: count}, nil
	case "retry":
		var count int = 1
		if val, ok := params["attempts"]; ok {
			switch v := val.(type) {
			case float64:
				count = int(v)
			case int:
				count = v
			case string:
				if c, err := strconv.Atoi(v); err == nil {
					count = c
				} else {
				logger.GetActionLogger().GetRawLogger().Warnf("Invalid 'attempts' for retry: %s", v)
			}
		default:
			logger.GetActionLogger().GetRawLogger().Warnf("Unsupported 'attempts' type for retry: %T", v)
			}
		}
		return &RetryAction{RetryCount: count}, nil
	case "banip":
		var durationStr string = "reboot" // 默认为重启
		if val, ok := params["duration"]; ok {
			switch v := val.(type) {
			case string:
				durationStr = v
			case int:
				durationStr = strconv.Itoa(v)
			case float64:
				durationStr = strconv.Itoa(int(v))
			default:
			logger.GetActionLogger().GetRawLogger().Warnf("Invalid 'duration' type for banip: %T, using default '%s'", val, durationStr)
			}
		}
		return &BanIPAction{Duration: durationStr}, nil
	case "banipdomain":
		var durationStr string = "reboot"
		var scope string = "domain"
		if val, ok := params["duration"]; ok {
			switch v := val.(type) {
			case string:
				durationStr = v
			case int:
				durationStr = strconv.Itoa(v)
			case float64:
				durationStr = strconv.Itoa(int(v))
			default:
				logger.GetActionLogger().GetRawLogger().Warnf("Invalid 'duration' type for banipdomain: %T", val)
			}
		}
		if val, ok := params["scope"].(string); ok {
			scope = val
		}
		return &BanIPDomainAction{Duration: durationStr, Scope: scope}, nil
	case "save_to_pool":
		tier := "auto"
		domainSpecific := false
		minScore := constants.DefaultQualityScore
		if val, ok := params["quality_tier"].(string); ok {
			tier = val
		}
		if val, ok := params["domain_specific"].(bool); ok {
			domainSpecific = val
		}
		if val, ok := params["min_score"]; ok {
			switch v := val.(type) {
			case float64:
				minScore = v
			case int:
				minScore = float64(v)
			case string:
				if ms, err := strconv.ParseFloat(v, 64); err == nil {
					minScore = ms
				}
			}
		}
		return &SaveToPoolAction{QualityTier: tier, DomainSpecific: domainSpecific, MinScore: minScore}, nil
	case "use_quality_proxy":
		preferredTier := "auto"
		fallbackTier := ""
		domainFirst := true // 默认为true
		if val, ok := params["preferred_tier"].(string); ok {
			preferredTier = val
		}
		if val, ok := params["fallback_tier"].(string); ok {
			fallbackTier = val
		}
		if val, ok := params["domain_first"].(bool); ok {
			domainFirst = val
		}
		return &QualityProxyAction{PreferredTier: preferredTier, FallbackTier: fallbackTier, DomainFirst: domainFirst}, nil
	case "cache":
		duration := 300000
		maxUse := 0
		cacheScope := "url"
		customKey := ""
		ignoreParams := false
		if val, ok := params["duration"]; ok {
			switch v := val.(type) {
			case float64:
				duration = int(v)
			case int:
				duration = v
			case string:
				if d, err := strconv.Atoi(v); err == nil {
					duration = d
				}
			}
		}
		if val, ok := params["max_use_count"]; ok {
			switch v := val.(type) {
			case float64:
				maxUse = int(v)
			case int:
				maxUse = v
			case string:
				if mu, err := strconv.Atoi(v); err == nil {
					maxUse = mu
				}
			}
		}
		if val, ok := params["cache_scope"].(string); ok {
			cacheScope = val
		}
		if val, ok := params["custom_key"].(string); ok {
			customKey = val
		}
		if val, ok := params["ignore_params"].(bool); ok {
			ignoreParams = val
		}
		return &CacheAction{Duration: duration, MaxUseCount: maxUse, CacheScope: cacheScope, CustomKey: customKey, IgnoreParams: ignoreParams}, nil
	case "request_url":
		action := &RequestURLAction{Method: "GET", TimeoutMS: constants.DefaultActionTimeoutMS, FollowRedirect: true, MaxRedirects: constants.DefaultMaxRedirects, ValidateSSL: true, ProxyOption: "current"} // 合理的默认值
		if val, ok := params["url"].(string); ok {
			action.URL = val
		} else {
			return nil, errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionParamMissing, "request_url: 'url' is required")
		}
		if val, ok := params["method"].(string); ok {
			action.Method = strings.ToUpper(val)
		}
		if val, ok := params["body"].(string); ok {
			action.Body = val
		}
		if val, ok := params["body_type"].(string); ok {
			action.BodyType = val
		}
		if val, ok := params["headers"].(string); ok {
			action.Headers = val
		}
		if val, ok := params["timeout_ms"]; ok {
			switch v := val.(type) {
			case float64:
				action.TimeoutMS = int(v)
			case int:
				action.TimeoutMS = v
			case string:
				if t, err := strconv.Atoi(v); err == nil {
					action.TimeoutMS = t
				}
			}
		}
		if val, ok := params["follow_redirect"].(bool); ok {
			action.FollowRedirect = val
		}
		if val, ok := params["max_redirects"]; ok {
			switch v := val.(type) {
			case float64:
				action.MaxRedirects = int(v)
			case int:
				action.MaxRedirects = v
			case string:
				if i, err := strconv.Atoi(v); err == nil {
					action.MaxRedirects = i
				}
			}
		}
		if val, ok := params["proxy_option"].(string); ok {
			action.ProxyOption = val
		}
		if val, ok := params["retry_count"]; ok {
			switch v := val.(type) {
			case float64:
				action.RetryCount = int(v)
			case int:
				action.RetryCount = v
			case string:
				if i, err := strconv.Atoi(v); err == nil {
					action.RetryCount = i
				}
			}
		}
		if val, ok := params["retry_delay_ms"]; ok {
			switch v := val.(type) {
			case float64:
				action.RetryDelayMS = int(v)
			case int:
				action.RetryDelayMS = v
			case string:
				if i, err := strconv.Atoi(v); err == nil {
					action.RetryDelayMS = i
				}
			}
		}
		if val, ok := params["validate_ssl"].(bool); ok {
			action.ValidateSSL = val
		}
		if val, ok := params["user_agent"].(string); ok {
			action.UserAgent = val
		}
		if val, ok := params["copy_headers"].(bool); ok {
			action.CopyHeaders = val
		}
		if val, ok := params["save_response"].(bool); ok {
			action.SaveResponse = val
		}
		if val, ok := params["save_response_path"].(string); ok {
			action.SaveResponsePath = val
		}

		if val, ok := params["validate_status_codes"]; ok {
			switch v := val.(type) {
			case []interface{}: // YAML 数字列表可能是 []interface{}
				for _, item := range v {
					if codeFloat, okF := item.(float64); okF {
						action.ValidateStatusCodes = append(action.ValidateStatusCodes, int(codeFloat))
					} else if codeInt, okI := item.(int); okI {
						action.ValidateStatusCodes = append(action.ValidateStatusCodes, codeInt)
					} else if codeStr, okS := item.(string); okS {
						if code, err := strconv.Atoi(codeStr); err == nil {
							action.ValidateStatusCodes = append(action.ValidateStatusCodes, code)
						}
					}
				}
			case []int:
				action.ValidateStatusCodes = v
			}
		}
		if val, ok := params["extract_headers"].(map[string]interface{}); ok {
			action.ExtractHeaders = make(map[string]string)
			for k, v_iface := range val {
				if v_str, ok_str := v_iface.(string); ok_str {
					action.ExtractHeaders[k] = v_str
				}
			}
		} else if val, ok := params["extract_headers"].(map[string]string); ok { // 直接的 map[string]string
			action.ExtractHeaders = val
		}
		// 日志级别通常不会以这种方式为每个动作实例配置
		return action, nil
	case "null_response":
		statusCode := http.StatusOK
		contentType := "text/plain"
		body := ""
		customHeaders := ""
		if val, ok := params["status_code"]; ok {
			switch v := val.(type) {
			case float64:
				statusCode = int(v)
			case int:
				statusCode = v
			case string:
				if sc, err := strconv.Atoi(v); err == nil {
					statusCode = sc
				}
			}
		}
		if val, ok := params["content_type"].(string); ok {
			contentType = val
		}
		if val, ok := params["body"].(string); ok {
			body = val
		}
		if val, ok := params["headers"].(string); ok {
			customHeaders = val
		}
		return &NullResponseAction{StatusCode: statusCode, ContentType: contentType, Body: body, Headers: customHeaders}, nil
	case "bypass_proxy":
		timeoutMs := constants.DefaultRequestTimeoutMS
		if val, ok := params["timeout_ms"]; ok { // 优先使用特定的 'timeout_ms'
			switch v := val.(type) {
			case float64:
				timeoutMs = int(v)
			case int:
				timeoutMs = v
			case string:
				if t, err := strconv.Atoi(v); err == nil {
					timeoutMs = t
				}
			}
		} else if val, ok := params["timeout"]; ok { // 回退到 'timeout'
			switch v := val.(type) {
			case float64:
				timeoutMs = int(v)
			case int:
				timeoutMs = v
			case string:
				if t, err := strconv.Atoi(v); err == nil {
					timeoutMs = t
				}
			}
		}
		return &BypassProxyAction{Timeout: time.Duration(timeoutMs) * time.Millisecond}, nil
	default:
		return nil, errors.NewErrorWithDetails(errors.ErrTypeAction, errors.ErrCodeActionTypeUnknown, "未知的动作类型", fmt.Sprintf("类型: %s", actionCfg.Type))
	}
}

// cloneResponse creates a deep copy of an http.Response
func cloneResponse(resp *http.Response, bodyBytes []byte) *http.Response {
	if resp == nil {
		return nil
	}
	cloned := &http.Response{
		Status:        resp.Status,
		StatusCode:    resp.StatusCode,
		Proto:         resp.Proto,
		ProtoMajor:    resp.ProtoMajor,
		ProtoMinor:    resp.ProtoMinor,
		Header:        resp.Header.Clone(),
		Body:          io.NopCloser(bytes.NewBuffer(bodyBytes)), // 使用传递的 bodyBytes
		ContentLength: int64(len(bodyBytes)),                    // 根据实际主体设置 ContentLength
		Trailer:       resp.Trailer.Clone(),
		Request:       resp.Request,
	}
	if resp.TLS != nil {
		cloned.TLS = &tls.ConnectionState{}
		*cloned.TLS = *resp.TLS
	}
	return cloned
}

// cloneRequest creates a deep copy of an http.Request
// 重要：如果读取原始请求的主体，它将被消耗。
// 此版本尝试恢复它。
func cloneRequest(req *http.Request) (*http.Request, error) {
	if req == nil {
		return nil, nil
	}

	ctx := req.Context() // 保留原始上下文

	var bodyBytes []byte
	var err error
	if req.Body != nil {
		bodyBytes, err = io.ReadAll(req.Body)
		if err != nil {
			// 即使出错也尝试恢复主体
			req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "读取请求体失败")
		}
		// 恢复原始主体
		req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	// 使用原始方法、URL 和主体的新读取器创建新请求
	clonedReq, err := http.NewRequestWithContext(ctx, req.Method, req.URL.String(), bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecFailed, "创建新请求失败")
	}

	// 复制头部
	clonedReq.Header = req.Header.Clone()

	// 复制其他相关字段
	clonedReq.ContentLength = req.ContentLength // 如果主体被转换，这可能是不正确的
	if req.Body != nil && bodyBytes != nil {    // 如果我们有主体字节，重新计算
		clonedReq.ContentLength = int64(len(bodyBytes))
	}
	clonedReq.TransferEncoding = req.TransferEncoding
	clonedReq.Close = req.Close
	clonedReq.Host = req.Host
	clonedReq.Form = req.Form                   // 如果未解析则为nil，如果已解析则为副本
	clonedReq.PostForm = req.PostForm           // 与Form相同
	clonedReq.MultipartForm = req.MultipartForm // 相同
	clonedReq.Trailer = req.Trailer.Clone()
	// RemoteAddr、RequestURI通常不需要用于从克隆发出的传出客户端请求

	return clonedReq, nil
}

// --- 缓存管理函数 ---
type CacheStats struct {
	TotalItems    int
	MemoryUsage   int64 // 以字节为单位估算
	ScopeCounts   map[string]int
	OldestItem    time.Time
	NewestItem    time.Time
	AvgAgeSeconds float64
	TotalUses     int
	AvgUsePerItem float64
}

func GetCacheStats() *CacheStats {
	responseCacheLock.RLock()
	defer responseCacheLock.RUnlock()
	stats := &CacheStats{TotalItems: len(responseCache), ScopeCounts: make(map[string]int)}
	// ... (GetCacheStats 实现的其余部分)
	return stats
}
func CleanExpiredCache() int {
	responseCacheLock.Lock()
	defer responseCacheLock.Unlock()
	cleanedCount := 0
	now := time.Now()
	for key, item := range responseCache {
		expiredByTime := !item.ExpireAt.IsZero() && now.After(item.ExpireAt)
		maxUseReached := item.MaxUseCount > 0 && item.CurrentUseCount >= item.MaxUseCount
		if expiredByTime || maxUseReached {
			delete(responseCache, key)
			cleanedCount++
		}
	}
	return cleanedCount
}
func CleanCacheByScope(scope string) int {
	responseCacheLock.Lock()
	defer responseCacheLock.Unlock()
	cleanedCount := 0
	prefix := scope + "::"
	for key := range responseCache {
		if strings.HasPrefix(key, prefix) {
			delete(responseCache, key)
			cleanedCount++
		}
	}
	return cleanedCount
}
func ClearAllCache() int {
	responseCacheLock.Lock()
	defer responseCacheLock.Unlock()
	count := len(responseCache)
	responseCache = make(map[string]*CachedResponse)
	return count
}

// 已删除或完全注释掉的旧函数的占位符。
// 这有助于确保write_to_file的行数更准确
// 如果工具对行数的显著减少敏感的话。
/*
func ParseActions(actionStr string) []Action { return nil }
func splitActions(actionStr string) []string { return nil }
func ParseActionGroup(actionGroup []string) []Action { return nil }
func (am *ActionManager) GetActionsForTrigger(triggerType string, triggerValue string, actionGroupName string) []Action { return nil }
func (am *ActionManager) GetActions(actionNames []string) []Action { return nil }
*/
