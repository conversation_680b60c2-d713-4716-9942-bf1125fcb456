package common

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/mubeng/mubeng/common/errors"
)

// ConfigValidator 配置验证器
type ConfigValidator struct {
	validator *validator.Validate
}

// NewConfigValidator 创建新的配置验证器
func NewConfigValidator() *ConfigValidator {
	v := validator.New()
	
	// 注册自定义验证规则
	v.RegisterValidation("duration_or_reboot", validateDurationOrReboot)
	v.RegisterValidation("action_type", validateActionType)
	v.RegisterValidation("trigger_type", validateTriggerType)
	
	return &ConfigValidator{
		validator: v,
	}
}

// ValidateConfig 验证配置结构
func (cv *ConfigValidator) ValidateConfig(config *Config) error {
	if config == nil {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "配置不能为空")
	}
	
	err := cv.validator.Struct(config)
	if err != nil {
		return cv.formatValidationError(err)
	}
	
	// 执行业务逻辑验证
	if err := cv.validateBusinessLogic(config); err != nil {
		return err
	}
	
	return nil
}

// validateBusinessLogic 执行业务逻辑验证
func (cv *ConfigValidator) validateBusinessLogic(config *Config) error {
	// 验证动作序列引用
	for eventName, event := range config.Events {
		for _, match := range event.Matches {
			for _, action := range match.Actions {
				if action.Type == "" {
					return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "事件中的动作类型不能为空", fmt.Sprintf("事件名称: %v", eventName))
				}
			}
		}
	}
	
	// 验证触发器条件
	for eventIndex, event := range config.Events {
		// 事件必须至少有一个条件、匹配规则或条件动作
		if len(event.Conditions) == 0 && len(event.Matches) == 0 && len(event.ConditionalActions) == 0 {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "事件必须至少有一个条件、匹配规则或条件动作", fmt.Sprintf("事件索引: %d, 事件名称: %s", eventIndex, event.Name))
		}
	}
	
	return nil
}

// formatValidationError 格式化验证错误信息
func (cv *ConfigValidator) formatValidationError(err error) error {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		var errorMessages []string
		
		for _, fieldError := range validationErrors {
			fieldName := cv.getFieldName(fieldError)
			errorMsg := cv.getErrorMessage(fieldError)
			errorMessages = append(errorMessages, fmt.Sprintf("%s: %s", fieldName, errorMsg))
		}
		
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "配置验证失败", strings.Join(errorMessages, "\n"))
	}
	
	return errors.WrapError(err, errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "配置验证失败")
}

// getFieldName 获取字段名称
func (cv *ConfigValidator) getFieldName(fieldError validator.FieldError) string {
	namespace := fieldError.Namespace()
	// 移除结构体名称前缀
	if idx := strings.Index(namespace, "."); idx != -1 {
		return namespace[idx+1:]
	}
	return namespace
}

// getErrorMessage 获取错误信息
func (cv *ConfigValidator) getErrorMessage(fieldError validator.FieldError) string {
	switch fieldError.Tag() {
	case "required":
		return "此字段是必需的"
	case "ip":
		return "必须是有效的IP地址"
	case "fqdn":
		return "必须是有效的域名"
	case "min":
		return fmt.Sprintf("最小值为 %s", fieldError.Param())
	case "max":
		return fmt.Sprintf("最大值为 %s", fieldError.Param())
	case "oneof":
		return fmt.Sprintf("必须是以下值之一: %s", fieldError.Param())
	case "dive":
		return "数组或切片元素验证失败"
	default:
		return fmt.Sprintf("验证失败: %s", fieldError.Tag())
	}
}

// 自定义验证函数

// validateDurationOrReboot 验证duration字段（可以是int或"reboot"字符串）
func validateDurationOrReboot(fl validator.FieldLevel) bool {
	value := fl.Field()
	
	switch value.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return value.Int() >= 0
	case reflect.String:
		return value.String() == "reboot"
	case reflect.Interface:
		// 处理interface{}类型
		if value.IsNil() {
			return false
		}
		actualValue := value.Elem()
		switch actualValue.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return actualValue.Int() >= 0
		case reflect.String:
			return actualValue.String() == "reboot"
		}
	}
	
	return false
}

// validateActionType 验证动作类型
func validateActionType(fl validator.FieldLevel) bool {
	actionType := fl.Field().String()
	validTypes := []string{
		"retry_same", "retry", "save_to_pool", "cache",
		"request_url", "banip", "banipdomain", "null_response",
		"bypass_proxy", "log", "block_request", "modify_request",
		"modify_response", "cache_response", "script",
	}
	
	for _, validType := range validTypes {
		if actionType == validType {
			return true
		}
	}
	return false
}

// validateTriggerType 验证触发器类型
func validateTriggerType(fl validator.FieldLevel) bool {
	triggerType := fl.Field().String()
	validTypes := []string{
		"status", "body", "max_request_time", "conn_time_out",
		"min_request_time", "url", "domain", "combined",
		"custom", "request_body", "request_header", "response_header",
	}
	
	for _, validType := range validTypes {
		if triggerType == validType {
			return true
		}
	}
	return false
}