package main

import (
	"fmt"
	"io/ioutil"
	"os"

	"gopkg.in/yaml.v3"
	"github.com/mubeng/mubeng/common"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run detailed_yaml_debug.go <yaml文件>")
		os.Exit(1)
	}

	filename := os.Args[1]
	fmt.Printf("=== 详细YAML调试: %s ===\n", filename)

	// 1. 读取文件
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		fmt.Printf("❌ 读取文件错误: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("✅ 文件读取成功，大小: %d 字节\n", len(data))

	// 2. 基本YAML解析测试
	var basicConfig interface{}
	err = yaml.Unmarshal(data, &basicConfig)
	if err != nil {
		fmt.Printf("❌ 基本YAML解析失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("✅ 基本YAML解析成功")

	// 3. 解析为Config结构体
	var config common.Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("❌ Config结构体解析失败: %v\n", err)
		// 尝试逐行解析找出问题
		lines := string(data)
		fmt.Printf("文件内容前500字符:\n%s\n", lines[:min(500, len(lines))])
		os.Exit(1)
	}
	fmt.Println("✅ Config结构体解析成功")

	// 4. 显示解析后的基本信息
	fmt.Printf("Global.Enable: %v\n", config.Global.Enable)
	fmt.Printf("Global.ProxyFile: %s\n", config.Global.ProxyFile)
	fmt.Printf("Global.DNSLookupMode: %s\n", config.Global.DNSLookupMode)
	fmt.Printf("CustomDNSServers数量: %d\n", len(config.Global.CustomDNSServers))
	fmt.Printf("Actions数量: %d\n", len(config.Actions))
	fmt.Printf("Events数量: %d\n", len(config.Events))

	fmt.Println("✅ 详细调试完成")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
