# FlexProxy HTTPS 证书管理系统

## 概述

FlexProxy 的 HTTPS 证书管理系统解决了在使用 HTTP 代理处理 HTTPS 流量时出现的证书警告问题。当 FlexProxy 作为 HTTP 代理拦截 HTTPS 流量时，浏览器会显示证书警告，因为使用的是 FlexProxy 自签名的证书而不是目标网站的原始证书。

## 问题说明

### 为什么会出现证书警告？

1. **HTTPS 代理工作原理**：当 FlexProxy 作为 HTTP 代理处理 HTTPS 请求时，它需要：
   - 拦截客户端到目标服务器的 HTTPS 连接
   - 解密 HTTPS 流量以进行处理（如修改请求/响应）
   - 重新加密并转发到目标服务器

2. **证书问题**：为了解密 HTTPS 流量，FlexProxy 必须：
   - 向客户端提供一个证书来建立 TLS 连接
   - 这个证书是 FlexProxy 自签名的，不是目标网站的原始证书
   - 浏览器检测到证书不匹配，显示安全警告

## 解决方案

FlexProxy 的证书管理系统通过以下方式解决这个问题：

### 1. 自动 CA 证书生成
- 在首次启动时自动生成 FlexProxy Root CA 证书
- CA 证书有效期为 10 年
- 使用 RSA 2048 位密钥

### 2. 动态域名证书生成
- 为每个访问的域名动态生成证书
- 证书由 FlexProxy CA 签名
- 支持域名和 IP 地址
- 证书缓存以提高性能

### 3. 跨平台证书安装
- 支持 Windows、macOS、Linux
- 自动检测系统类型
- 提供自动安装和手动安装选项

## 使用方法

### 自动设置（推荐）

1. **启动 FlexProxy**：
   ```bash
   go run main.go -a 127.0.0.1:8080 -f proxies.txt -config config.yaml
   ```

2. **证书检查**：
   - FlexProxy 启动时会自动检查证书状态
   - 如果证书未安装，会显示安装选项

3. **选择安装方式**：
   - 选择 `1` 进行自动安装（需要管理员权限）
   - 选择 `2` 生成安装脚本
   - 选择 `3` 显示手动安装说明

### 手动安装证书

#### macOS

**方法 1 - 命令行（推荐）**：
```bash
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain "./certs/ca-cert.pem"
```

**方法 2 - 图形界面**：
1. 打开"钥匙串访问"应用
2. 将 `ca-cert.pem` 文件拖拽到"系统"钥匙串
3. 双击证书，展开"信任"部分
4. 将"使用此证书时"设置为"始终信任"

#### Windows

**方法 1 - PowerShell（推荐）**：
```powershell
Import-Certificate -FilePath ".\certs\ca-cert.pem" -CertStoreLocation "Cert:\LocalMachine\Root"
```

**方法 2 - 图形界面**：
1. 双击 `ca-cert.pem` 文件
2. 点击"安装证书"
3. 选择"本地计算机"
4. 选择"将所有的证书都放入下列存储" -> "受信任的根证书颁发机构"

#### Linux

**Ubuntu/Debian**：
```bash
sudo cp ./certs/ca-cert.pem /usr/local/share/ca-certificates/flexproxy-ca.crt
sudo update-ca-certificates
```

**CentOS/RHEL**：
```bash
sudo cp ./certs/ca-cert.pem /etc/pki/ca-trust/source/anchors/flexproxy-ca.crt
sudo update-ca-trust
```

**Arch Linux**：
```bash
sudo cp ./certs/ca-cert.pem /etc/ca-certificates/trust-source/anchors/flexproxy-ca.crt
sudo trust extract-compat
```

## 浏览器配置

### Chrome/Edge
证书安装到系统后，Chrome 和 Edge 会自动信任。

### Firefox
Firefox 使用独立的证书存储：
1. 设置 -> 隐私与安全 -> 证书 -> 查看证书
2. 证书颁发机构 -> 导入
3. 选择 `ca-cert.pem` 文件
4. 勾选"信任此 CA 来标识网站"

### Safari (macOS)
系统证书安装后，Safari 会自动信任。

## 技术细节

### 证书结构
- **CA 证书**：FlexProxy Root CA
  - 有效期：10 年
  - 密钥长度：RSA 2048 位
  - 用途：证书签名

- **域名证书**：动态生成
  - 有效期：1 年
  - 密钥长度：RSA 2048 位
  - SAN 支持：域名和 IP 地址

### 文件位置
```
./certs/
├── ca-cert.pem          # CA 证书（公钥）
├── ca-key.pem           # CA 私钥
└── scripts/
    ├── install-cert.sh  # macOS/Linux 安装脚本
    └── install-cert.ps1 # Windows 安装脚本
```

### 安全考虑
1. **CA 私钥保护**：CA 私钥仅存储在本地，不会传输
2. **证书有效期**：合理的证书有效期，避免长期风险
3. **权限控制**：证书文件使用适当的文件权限
4. **自动清理**：过期证书自动清理（计划中）

## 故障排除

### 常见问题

**Q: 安装证书后仍然显示警告？**
A: 
1. 确认证书已正确安装到系统信任存储
2. 重启浏览器
3. 检查证书是否设置为"始终信任"

**Q: 自动安装失败？**
A: 
1. 确保有管理员权限
2. 使用手动安装方法
3. 检查系统防病毒软件是否阻止

**Q: Firefox 仍然显示警告？**
A: Firefox 使用独立证书存储，需要单独导入证书

### 日志调试
启用详细日志查看证书管理过程：
```bash
go run main.go -v -a 127.0.0.1:8080 -f proxies.txt -config config.yaml
```

## 测试工具

### 证书系统测试
```bash
go run tools/test_certificate.go
```

### 服务器启动测试
```bash
go run tools/test_server_startup.go
```

## 注意事项

1. **首次使用**：首次启动时需要生成证书，可能需要几秒钟
2. **权限要求**：自动安装证书需要管理员权限
3. **防病毒软件**：某些防病毒软件可能阻止证书安装
4. **企业环境**：企业环境可能有证书策略限制
5. **浏览器重启**：安装证书后建议重启浏览器

## 开发者信息

### API 接口
```go
// 创建证书管理器
certManager, err := certificate.NewCertificateManager(config, logger)

// 获取域名证书
cert, err := certManager.GetCertificate("example.com")

// 检查证书安装状态
installed, err := certManager.CheckCertificateInstallation()

// 安装证书
err := certManager.InstallCertificate()
```

### 配置选项
```go
config := &certificate.Config{
    CertDir:     "./certs",      // 证书存储目录
    AutoInstall: true,           // 是否自动安装
}
```
