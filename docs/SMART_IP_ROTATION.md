# FlexProxy 智能IP轮换模式

## 概述

FlexProxy 的智能IP轮换模式（`smart`）是一个高级代理选择策略，能够根据触发的动作类型智能决定是否切换代理IP。这个模式特别适用于需要根据不同情况灵活处理代理切换的场景。

## 功能特性

### 1. 智能决策机制
- **上下文感知**: 检查请求上下文中的动作信息
- **动作类型识别**: 区分不同类型的重试动作
- **自适应切换**: 根据动作类型自动决定是否切换代理

### 2. 支持的动作类型
- **retry**: 使用新代理重试 - 会切换到新的代理IP
- **retry_same**: 使用相同代理重试 - 继续使用当前代理IP
- **正常请求**: 没有重试标记时，继续使用当前代理

### 3. 代理选择策略
- **质量优先**: 优先选择高质量代理池中的代理
- **智能回退**: 质量池不可用时回退到随机选择
- **缓存优化**: 合理利用代理缓存，避免频繁切换

## 配置方法

### 1. 全局配置
在 `config.yaml` 中设置：

```yaml
global:
  ip_rotation_mode: "smart"
```

### 2. 支持的模式值
- `random`: 随机选择
- `sequential`: 顺序选择  
- `quality`: 基于质量评分选择
- `smart`: 智能选择（新增）

## 工作原理

### 1. 上下文检查
智能模式会检查请求上下文中的以下键值：
- `retry_action_type`: 重试动作类型（"retry" 或 "retry_same"）
- `action_requires_retry`: 是否需要重试的布尔标记

### 2. 决策逻辑
```
如果 retry_action_type == "retry":
    切换到新代理
否则如果 retry_action_type == "retry_same":
    继续使用当前代理
否则:
    使用缓存代理（如果可用且未过期）
```

### 3. 代理选择优先级
1. **重试场景**: 根据重试类型决定是否切换
2. **质量池**: 优先从高质量代理池选择
3. **随机选择**: 回退策略，确保总能选到代理

## 使用示例

### 1. 触发器配置示例
```yaml
events:
  - name: "google_tag_manager_detection"
    triggers:
      - type: "conditional"
        conditions:
          - field: "response_body"
            operator: "contains"
            value: "googletagmanager.com"
        actions:
          - type: "retry"  # 使用新代理重试
            retry_count: 3
```

### 2. 动作系统集成
智能模式与现有的动作系统无缝集成：
- `RetryAction`: 设置 `retry_action_type: "retry"`
- `RetrySameAction`: 设置 `retry_action_type: "retry_same"`

## 性能优化

### 1. 缓存机制
- 代理缓存时间: 30秒
- 避免频繁的代理切换
- 提高请求处理效率

### 2. 质量评估
- 基于成功率和响应时间的质量评分
- 动态调整代理选择策略
- 自动排除低质量代理

### 3. 故障恢复
- 自动检测代理可用性
- 智能回退到备用策略
- 确保服务连续性

## 监控和调试

### 1. 日志输出
智能模式会输出详细的选择日志：
```
智能模式：继续使用缓存代理 proxy1:8080
智能模式：从质量池选择代理 proxy2:8080  
智能模式：随机选择代理 proxy3:8080
```

### 2. 指标统计
- 代理切换频率
- 质量池使用率
- 缓存命中率

## 最佳实践

### 1. 配置建议
- 确保代理池有足够的代理数量
- 合理配置质量评估参数
- 定期监控代理性能

### 2. 使用场景
- **Web爬虫**: 根据反爬策略智能切换代理
- **API调用**: 基于响应状态决定重试策略
- **负载测试**: 模拟真实用户行为模式

### 3. 故障排除
- 检查上下文信息是否正确设置
- 验证动作配置是否正确
- 监控代理池状态和质量指标

## 技术实现

### 1. 核心组件
- `SmartProxyStrategy`: 智能代理选择策略
- `ProxyManager.smartProxy()`: 代理管理器智能选择方法
- `ProxyService.getSmartProxy()`: 服务层智能选择方法

### 2. 集成点
- 配置验证: `config_types.go`
- 常量定义: `constants.go`
- 策略实现: `proxy_strategies.go`
- 服务集成: `proxy_service.go`

## 版本兼容性

- 向后兼容现有的 `random`、`sequential`、`quality` 模式
- 新增 `smart` 模式不影响现有功能
- 平滑升级，无需修改现有配置（除非要使用智能模式）
