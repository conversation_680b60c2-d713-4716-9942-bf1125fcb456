2025-07-07 20:06:49,387 - INFO - context_portal_mcp.main - File logging configured to: ./context_portal/./logs/conport.log
2025-07-07 20:06:49,387 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='.', mode='stdio', log_file='./logs/conport.log', log_level='INFO')
2025-07-07 20:06:49,388 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: .
2025-07-07 20:06:49,388 - INFO - context_portal_mcp.db.database - Alembic.ini not found. Creating at context_portal/alembic.ini
2025-07-07 20:06:49,388 - INFO - context_portal_mcp.db.database - Alembic env.py not found. Creating at context_portal/alembic/env.py
2025-07-07 20:06:49,388 - INFO - context_portal_mcp.db.database - Initial schema not found. Creating at context_portal/alembic/versions/2025_06_17_initial_schema.py
2025-07-07 20:06:49,389 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '.'. Database directory will be created on first actual DB use.
2025-07-07 20:06:49,407 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:13:34,045 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/./logs/conport.log
2025-07-07 20:13:34,045 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='./logs/conport.log', log_level='INFO')
2025-07-07 20:13:34,045 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:13:34,045 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:13:34,060 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:13:34,063 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-07 20:14:24,985 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/./logs/conport.log
2025-07-07 20:14:24,985 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='./logs/conport.log', log_level='INFO')
2025-07-07 20:14:24,985 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:14:24,985 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:14:25,005 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:14:25,009 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-07 20:14:31,917 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/./logs/conport.log
2025-07-07 20:14:31,917 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='./logs/conport.log', log_level='INFO')
2025-07-07 20:14:31,917 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:14:31,917 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:14:31,930 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:14:31,936 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-07 20:15:25,469 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-07 20:15:25,469 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-07 20:15:25,469 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:15:25,469 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:15:25,481 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:15:25,485 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-07 20:17:41,325 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-07 20:17:41,325 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-07 20:17:41,326 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:17:41,326 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:17:41,342 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:17:41,347 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-07 20:17:49,239 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-07 20:17:49,240 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-07 20:17:49,240 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:17:49,240 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:17:49,256 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:17:49,261 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-07 20:24:30,114 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-07 20:24:30,115 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-07 20:24:30,115 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:24:30,115 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:24:30,130 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:24:30,136 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-07 20:24:37,137 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-07 20:24:37,138 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-07 20:24:37,138 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:24:37,138 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:24:37,155 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:24:37,159 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-07 20:40:30,335 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/./logs/conport.log
2025-07-07 20:40:30,335 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='./logs/conport.log', log_level='INFO')
2025-07-07 20:40:30,335 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-07 20:40:30,336 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-07 20:40:30,353 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-07 20:40:30,357 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-08 18:15:43,780 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-08 18:15:43,780 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-08 18:15:43,780 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-08 18:15:43,780 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-08 18:15:43,780 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-08 18:15:43,780 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-08 18:15:43,781 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-08 18:15:43,781 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-08 18:15:43,781 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-08 18:15:43,781 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-08 18:15:43,781 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-08 18:15:43,781 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-08 18:15:43,803 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-08 18:15:43,803 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-08 18:15:43,803 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-08 18:15:43,813 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-08 18:15:43,813 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-08 18:15:43,813 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-08 18:17:00,165 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-08 18:17:00,165 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-08 18:17:00,165 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-08 18:17:00,166 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-08 18:17:00,185 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-08 18:17:00,189 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-08 18:20:02,301 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan shutting down. Closing all DB connections.
2025-07-08 18:20:02,312 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan shutting down. Closing all DB connections.
2025-07-08 18:20:02,406 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan shutting down. Closing all DB connections.
2025-07-08 18:20:26,930 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-08 18:20:26,930 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-08 18:20:26,930 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-08 18:20:26,931 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-08 18:20:26,932 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-08 18:20:26,932 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-08 18:20:26,932 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-08 18:20:26,932 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-08 18:20:26,934 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-08 18:20:26,934 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-08 18:20:26,934 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-08 18:20:26,934 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-08 18:20:26,949 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-08 18:20:26,949 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-08 18:20:26,950 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-08 18:20:26,953 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-08 18:20:26,953 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-08 18:20:26,955 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
2025-07-08 18:20:45,216 - INFO - context_portal_mcp.main - File logging configured to: /Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log
2025-07-08 18:20:45,217 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='/Users/<USER>/Documents/Project Development/Golang/FlexProxy', mode='stdio', log_file='/Users/<USER>/Documents/Project Development/Golang/FlexProxy/context_portal/logs/conport.log', log_level='INFO')
2025-07-08 18:20:45,217 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: /Users/<USER>/Documents/Project Development/Golang/FlexProxy
2025-07-08 18:20:45,217 - INFO - context_portal_mcp.main - STDIO mode: Using effective_workspace_id '/Users/<USER>/Documents/Project Development/Golang/FlexProxy'. Database directory will be created on first actual DB use.
2025-07-08 18:20:45,233 - INFO - context_portal_mcp.main - ConPort FastMCP server lifespan starting.
2025-07-08 18:20:45,238 - INFO - mcp.server.lowlevel.server - Processing request of type ListToolsRequest
